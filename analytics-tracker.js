// analytics-tracker.js - Tracks adblock popup and conversion events

// Enhanced analytics tracking with additional client-side data
function gotham_send_analytics(eventType, status = 'pending') {
    // Check for localized AJAX variables first, then fallback to globals
    var ajaxUrl = '';
    var nonce = '';

    if (typeof gotham_ajax !== 'undefined') {
        ajaxUrl = gotham_ajax.ajaxurl;
        nonce = gotham_ajax.nonce;
    } else if (typeof ajaxurl !== 'undefined') {
        ajaxUrl = ajaxurl;
        if (typeof gotham_adblock_nonce !== 'undefined') {
            nonce = gotham_adblock_nonce;
        }
    } else {
        console.warn('Gotham Analytics: AJAX URL not available');
        return;
    }

    // Collect additional client-side data
    var additionalData = gotham_collect_client_data();

    var data = new FormData();
    data.append('action', 'gotham_adblock_track_event');
    data.append('event_type', eventType);
    data.append('status', status);
    data.append('nonce', nonce);

    // Add client-side data
    data.append('screen_resolution', additionalData.screen_resolution);
    data.append('timezone', additionalData.timezone);
    data.append('session_duration', additionalData.session_duration);
    data.append('page_url', additionalData.page_url);
    data.append('referrer_url', additionalData.referrer_url);

    console.log('Gotham Analytics: Sending event:', eventType, 'to:', ajaxUrl);

    fetch(ajaxUrl, { method: 'POST', body: data })
        .then(res => {
            console.log('Gotham Analytics: Response status:', res.status);
            return res.json();
        })
        .then(json => {
            if (json && json.success) {
                console.log('Gotham Analytics: Event sent successfully:', eventType, json);
            } else {
                console.warn('Gotham Analytics: Event failed:', eventType, json);
                // Show debug info if available
                if (json && json.data && json.data.debug) {
                    console.warn('Debug info:', json.data.debug);
                }
            }
        })
        .catch(e => {
            console.error('Gotham Analytics: AJAX error for event:', eventType, e);
            console.error('AJAX URL was:', ajaxUrl);
        });
}

// Collect additional client-side data
function gotham_collect_client_data() {
    var sessionStart = gotham_get_session_start();
    var sessionDuration = sessionStart ? Math.floor((Date.now() - sessionStart) / 1000) : 0;

    return {
        screen_resolution: screen.width + 'x' + screen.height,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || '',
        session_duration: sessionDuration,
        page_url: window.location.href,
        referrer_url: document.referrer || ''
    };
}

// Session management
function gotham_get_session_start() {
    var sessionStart = sessionStorage.getItem('gotham_session_start');
    if (!sessionStart) {
        sessionStart = Date.now();
        sessionStorage.setItem('gotham_session_start', sessionStart);
    }
    return parseInt(sessionStart);
}

// Track page visibility changes for better session tracking
function gotham_track_page_visibility() {
    var hidden = 'hidden';
    var visibilityChange = 'visibilitychange';

    if (typeof document.hidden !== 'undefined') {
        hidden = 'hidden';
        visibilityChange = 'visibilitychange';
    } else if (typeof document.msHidden !== 'undefined') {
        hidden = 'msHidden';
        visibilityChange = 'msvisibilitychange';
    } else if (typeof document.webkitHidden !== 'undefined') {
        hidden = 'webkitHidden';
        visibilityChange = 'webkitvisibilitychange';
    }

    document.addEventListener(visibilityChange, function() {
        if (document[hidden]) {
            // Page is hidden - user might be leaving
            gotham_send_analytics('page_hidden', 'tracking');
        } else {
            // Page is visible again
            gotham_send_analytics('page_visible', 'tracking');
        }
    });
}

console.log('Gotham Analytics: tracker script loaded');

// Initialize tracking when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize session tracking
    gotham_get_session_start();

    // Initialize page visibility tracking
    gotham_track_page_visibility();
});

// Use MutationObserver to detect popup display and send analytics event
(function() {
    let sent = false;
    let checkCount = 0;

    function sendIfPopupVisible() {
        checkCount++;
        const popup = document.getElementById('gothamadblock_msg');

        if (!sent && popup) {
            // Check if popup is actually visible
            const style = window.getComputedStyle(popup);
            const isVisible = style.display !== 'none' && style.visibility !== 'hidden' && popup.offsetHeight > 0;

            if (isVisible) {
                sent = true;
                console.log('Gotham Analytics: Popup detected and visible, sending analytics...');
                if (typeof gotham_send_analytics === 'function') {
                    gotham_send_analytics('pop_displayed');
                } else {
                    console.warn('Gotham Analytics: gotham_send_analytics function not available');
                }
            }
        }

        // Debug logging
        if (checkCount <= 5) {
            console.log('Gotham Analytics: Popup check #' + checkCount + ', popup found:', !!popup, 'sent:', sent);
        }
    }

    // Set up MutationObserver
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(sendIfPopupVisible);
        observer.observe(document.body, { childList: true, subtree: true });
        console.log('Gotham Analytics: MutationObserver set up for popup detection');
    } else {
        console.warn('Gotham Analytics: MutationObserver not supported, using fallback');
        // Fallback for older browsers
        setInterval(sendIfPopupVisible, 1000);
    }

    // Also check immediately in case popup is already present
    sendIfPopupVisible();

    // Additional check after a short delay
    setTimeout(sendIfPopupVisible, 500);
    setTimeout(sendIfPopupVisible, 2000);
})();

// Call this when user disables adblock and continues
window.gotham_adblock_conversion = function() {
    console.log('Gotham Analytics: Conversion triggered - user disabled adblock');
    gotham_send_analytics('adblock_disabled', 'completed');
}

// Enhanced button click tracking
document.addEventListener('DOMContentLoaded', function() {
    console.log('Gotham Analytics: Setting up button click tracking...');

    // Function to set up button tracking
    function setupButtonTracking() {
        const button = document.getElementById('gtab_mehn');
        if (button && !button.hasAttribute('data-gotham-tracked')) {
            button.setAttribute('data-gotham-tracked', 'true');
            console.log('Gotham Analytics: Button found, setting up click tracking');

            button.addEventListener('click', function() {
                console.log('Gotham Analytics: Button clicked, tracking popup_closed event');
                gotham_send_analytics('popup_closed', 'user_action');

                // Also track potential conversion after a delay
                setTimeout(function() {
                    console.log('Gotham Analytics: Checking for conversion after button click...');
                    // This will be called by the main plugin if adblock is actually disabled
                }, 1000);
            });
        }
    }

    // Try to set up tracking immediately
    setupButtonTracking();

    // Also try after popup might be created
    setTimeout(setupButtonTracking, 1000);
    setTimeout(setupButtonTracking, 3000);

    // Use MutationObserver to catch dynamically added buttons
    if (typeof MutationObserver !== 'undefined') {
        const buttonObserver = new MutationObserver(setupButtonTracking);
        buttonObserver.observe(document.body, { childList: true, subtree: true });
    }
});
