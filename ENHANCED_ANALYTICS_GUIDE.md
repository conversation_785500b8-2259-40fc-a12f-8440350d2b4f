# Enhanced Analytics Dashboard - User Guide

## Overview
The Gotham Block Extra Light plugin now features a comprehensive analytics dashboard that provides detailed insights into user behavior, conversion patterns, and geographic distribution. This guide explains how to use and interpret the enhanced analytics features.

## Dashboard Features

### 1. Time-Based Analytics
The dashboard now displays data across different time periods:

- **Daily View**: Shows data for the last 30 days (default)
- **Hourly View**: Shows data for the last 24 hours
- **Weekly View**: Shows data for the last 12 weeks

### 2. Summary Statistics Cards
Four key metric cards provide instant insights:

- **Total Users Detected**: Unique users who encountered the adblock popup
- **Adblock Disabled**: Users who successfully disabled their ad blocker
- **Popup Declined**: Users who closed the popup without taking action
- **Conversion Rate**: Percentage of users who disabled their ad blocker

### 3. User Behavior Over Time Chart
A line chart showing trends for:
- Popup displays
- Successful conversions
- Popup declines

### 4. Conversion Funnel
A visual funnel showing the user journey:
1. Users Detected (100%)
2. Users Interacted (interaction rate %)
3. Users Converted (conversion rate %)

### 5. Geographic Distribution
Bar chart showing top countries by:
- Total users
- Conversion numbers
- Country-specific conversion rates

### 6. Browser & Device Analytics
Donut chart displaying:
- Browser distribution
- Browser versions
- Mobile vs desktop usage

## Enhanced Data Tracking

### User Classification
Users are automatically classified as:
- **Unique**: First-time visitors
- **Returning**: Previous visitors
- **Converted**: Users who previously disabled ad blocker
- **Returning Declined**: Users who previously declined

### Session Tracking
The system now tracks:
- Session duration
- Page visibility changes
- User interaction patterns
- Screen resolution
- Timezone information

### Geographic Data
Enhanced location tracking includes:
- Country identification via IP
- Multiple API fallbacks for reliability
- Caching to reduce API calls
- Local/private IP detection

## Dashboard Controls

### Time Period Selector
Choose from:
- Daily (Last 30 days)
- Hourly (Last 24 hours)
- Weekly (Last 12 weeks)

### Days to Show Selector
Filter data by:
- Last 7 days
- Last 30 days (default)
- Last 90 days
- Last year

### Refresh Data Button
Manually refresh analytics data to see latest statistics.

## Data Accuracy Features

### IP-Based Deduplication
- Popup displays: Limited to once per hour per IP
- Conversions: Limited to once per day per IP
- Prevents duplicate counting from same users

### User Journey Tracking
- Tracks complete user interaction flow
- Identifies drop-off points
- Measures engagement levels

### Browser Detection
Enhanced detection for:
- Chrome, Firefox, Safari, Edge
- Brave, Vivaldi, Opera
- Mobile browsers
- Browser versions

## Accessing the Dashboard

1. **WordPress Admin**: Navigate to "G BlockAdblock" in the admin menu
2. **Analytics Tab**: Click on the analytics section
3. **Dashboard View**: View comprehensive analytics data

## Understanding the Metrics

### Conversion Rate Calculation
```
Conversion Rate = (Users who disabled adblock / Total users detected) × 100
```

### Interaction Rate Calculation
```
Interaction Rate = (Users who interacted / Total users detected) × 100
```

### User Type Distribution
- Shows breakdown of unique vs returning visitors
- Helps understand user behavior patterns
- Identifies most valuable user segments

## Technical Implementation

### Database Schema
Enhanced table structure includes:
- User classification fields
- Session tracking data
- Geographic information
- Browser and device details
- Timestamp indexing for performance

### Performance Optimizations
- Composite database indexes
- Efficient query structures
- Data caching mechanisms
- Reduced API calls

### Security Features
- Nonce verification for all AJAX requests
- Input sanitization and validation
- Capability checks for admin access
- SQL injection prevention

## Troubleshooting

### No Data Showing
1. Check if popup is being displayed to users
2. Verify JavaScript is loading correctly
3. Ensure database table exists and has proper structure
4. Run the test suite: `your-site.com/wp-admin/?gotham_test=1`

### Inaccurate Geographic Data
1. Check if IP detection is working
2. Verify API connectivity for geolocation
3. Review cached country data
4. Check for private/local IP addresses

### Missing Browser Data
1. Verify user agent detection
2. Check browser classification function
3. Review mobile device detection
4. Ensure proper data sanitization

## API Endpoints

### Get Analytics Data
```
GET /wp-admin/admin-ajax.php?action=gotham_adblock_get_stats&period=daily&days=30&_wpnonce=[nonce]
```

### Track Events
```
POST /wp-admin/admin-ajax.php
action: gotham_adblock_track_event
event_type: pop_displayed|adblock_disabled|popup_closed
status: pending|accepted|declined
nonce: [security_nonce]
```

## Data Export

The analytics data can be accessed programmatically through the WordPress database or via custom queries. All data is stored in the `wp_gotham_adblock_stats` table with proper indexing for efficient retrieval.

## Privacy Considerations

- IP addresses are used for deduplication only
- No personally identifiable information is stored
- Geographic data is country-level only
- Session data is temporary and anonymized
- Complies with privacy regulations when used appropriately

## Support and Maintenance

For optimal performance:
1. Regularly monitor database table size
2. Check API rate limits for geolocation services
3. Review analytics accuracy periodically
4. Update browser detection patterns as needed
5. Monitor dashboard loading performance

The enhanced analytics system provides comprehensive insights while maintaining user privacy and system performance.
