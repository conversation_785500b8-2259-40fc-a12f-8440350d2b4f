<?php
/**
 * Test Popup Analytics
 * This page simulates the adblock popup to test analytics tracking
 * Access via: /wp-content/plugins/gotham-block-extra-light/test-popup-analytics.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-load.php');
}

// Enqueue the analytics tracker
wp_enqueue_script('jquery');
wp_register_script('gotham-analytics-tracker', plugin_dir_url(__FILE__) . 'analytics-tracker.js', array('jquery'), '1.5.0', true);
wp_enqueue_script('gotham-analytics-tracker');

// Localize script with AJAX URL
wp_localize_script('gotham-analytics-tracker', 'gotham_ajax', array(
    'ajaxurl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('gotham_adblock_nonce')
));

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Popup Analytics - Gotham Block</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6; 
            background: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        
        .test-button { 
            display: inline-block; 
            margin: 10px 5px; 
            padding: 10px 20px; 
            background: #007cba; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            cursor: pointer; 
            border: none; 
            font-size: 14px;
        }
        
        .test-button:hover { background: #005a87; color: white; }
        .test-button.success { background: #28a745; }
        .test-button.success:hover { background: #218838; }
        .test-button.danger { background: #dc3545; }
        .test-button.danger:hover { background: #c82333; }
        
        .console-log { 
            background: #000; 
            color: #0f0; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            max-height: 400px; 
            overflow-y: auto; 
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        /* Popup Styles (simulating the real popup) */
        #gothamadblock_msg {
            position: fixed;
            width: 600px;
            margin: 0 auto;
            background: #fff;
            height: auto;
            display: none;
            z-index: 99999999;
            text-align: center;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            border-radius: 8px;
            border: 4px solid orange;
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        #gothamadblock_msg img {
            width: 150px;
            height: 150px;
            margin: 20px auto;
        }
        
        #gothamadblock_msg h2 {
            font-weight: 700;
            font-family: arial;
            padding: 10px 0;
            font-size: 26px;
            color: #333;
        }
        
        #gothamadblock_msg p {
            margin: 30px 0;
            color: #666;
            line-height: 1.6;
        }
        
        button#gtab_mehn {
            cursor: pointer;
            display: inline-block;
            text-align: center;
            vertical-align: middle;
            padding: 12px 24px;
            border: 1px solid #4443cf;
            border-radius: 8px;
            background: linear-gradient(to bottom, #807eff, #4443cf);
            font: normal normal bold 20px arial;
            color: #ffffff;
            text-decoration: none;
            margin: 20px 0;
        }
        
        button#gtab_mehn:hover {
            background: linear-gradient(to bottom, #9a97ff, #5250f8);
        }
        
        #gothamadblock_overlayh_n {
            position: fixed;
            width: 100%;
            margin: 0 auto;
            opacity: 0.8;
            background: #000;
            height: 100%;
            display: none;
            z-index: 99999998;
            top: 0;
            left: 0;
        }
        
        .gtmab_leviator {
            height: 100%;
            overflow: hidden;
        }
        
        @media (max-width: 768px) {
            #gothamadblock_msg {
                width: 90%;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Popup Analytics - Gotham Block</h1>
        
        <div class="test-section info">
            <h2>📋 Test Instructions</h2>
            <p>This page simulates the Gotham Block adblock detection popup to test the analytics tracking system.</p>
            <ol>
                <li><strong>Show Popup:</strong> Click "Show Test Popup" to display a simulated popup</li>
                <li><strong>Check Analytics:</strong> The popup display should be automatically tracked</li>
                <li><strong>Test Interactions:</strong> Click the popup button to test conversion tracking</li>
                <li><strong>View Results:</strong> Check the console output and visit the Analytics Dashboard</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🎮 Test Controls</h2>
            <button class="test-button" onclick="showTestPopup()">Show Test Popup</button>
            <button class="test-button" onclick="hideTestPopup()">Hide Test Popup</button>
            <button class="test-button success" onclick="testDirectTracking()">Test Direct Tracking</button>
            <button class="test-button danger" onclick="clearConsole()">Clear Console</button>
            
            <div style="margin-top: 15px;">
                <a href="<?php echo admin_url('admin.php?page=gotham-adblock-analytics'); ?>" class="test-button" target="_blank">
                    📊 View Analytics Dashboard
                </a>
                <a href="<?php echo plugin_dir_url(__FILE__); ?>debug-analytics.php" class="test-button" target="_blank">
                    🔍 Debug Tool
                </a>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📟 Console Output</h2>
            <div id="console-output" class="console-log">Analytics test page loaded. Ready for testing...\n</div>
        </div>
        
        <div class="test-section">
            <h2>📊 Current Analytics Status</h2>
            <div id="analytics-status">
                <?php
                global $wpdb;
                $table = $wpdb->prefix . 'gotham_adblock_stats';
                
                if ($wpdb->get_var("SHOW TABLES LIKE '$table'") == $table) {
                    $total_events = $wpdb->get_var("SELECT COUNT(*) FROM $table");
                    $popup_displays = $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE event_type = 'pop_displayed'");
                    $conversions = $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE event_type = 'adblock_disabled'");
                    $popup_closes = $wpdb->get_var("SELECT COUNT(*) FROM $table WHERE event_type = 'popup_closed'");
                    
                    echo '<div class="success">✅ Analytics table exists</div>';
                    echo '<div class="info">📈 Total events: ' . $total_events . '</div>';
                    echo '<div class="info">👁️ Popup displays: ' . $popup_displays . '</div>';
                    echo '<div class="info">✅ Conversions: ' . $conversions . '</div>';
                    echo '<div class="info">❌ Popup closes: ' . $popup_closes . '</div>';
                } else {
                    echo '<div class="error">❌ Analytics table does not exist</div>';
                }
                ?>
            </div>
        </div>
    </div>
    
    <!-- Simulated Popup HTML -->
    <div id="gothamadblock_msg" style="display:none;">
        <h2>🚫 AdBlock Detected!</h2>
        <img src="<?php echo plugin_dir_url(__FILE__); ?>stop.png" alt="Stop" onerror="this.style.display='none'" />
        <p>We've detected that you're using an ad blocker. Please disable your ad blocker to continue browsing our site and support our content.</p>
        <button id="gtab_mehn" onclick="handlePopupButton()">I've Disabled My AdBlock</button>
    </div>
    <div id="gothamadblock_overlayh_n" style="display:none;"></div>
    
    <?php wp_footer(); ?>
    
    <script>
    // Console logging function
    function log(message, type = 'info') {
        const output = document.getElementById('console-output');
        const timestamp = new Date().toLocaleTimeString();
        const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
        output.innerHTML += '[' + timestamp + '] ' + prefix + ' ' + message + '\n';
        output.scrollTop = output.scrollHeight;
    }
    
    // Test functions
    function showTestPopup() {
        log('Showing test popup...', 'info');
        document.getElementById('gothamadblock_msg').style.display = 'block';
        document.getElementById('gothamadblock_overlayh_n').style.display = 'block';
        document.body.classList.add('gtmab_leviator');
        
        // The analytics tracker should automatically detect this and send tracking
        setTimeout(() => {
            log('Popup should now be visible and tracked by analytics', 'success');
        }, 500);
    }
    
    function hideTestPopup() {
        log('Hiding test popup...', 'info');
        document.getElementById('gothamadblock_msg').style.display = 'none';
        document.getElementById('gothamadblock_overlayh_n').style.display = 'none';
        document.body.classList.remove('gtmab_leviator');
    }
    
    function handlePopupButton() {
        log('Popup button clicked - simulating user interaction', 'info');
        
        // Track popup close
        if (typeof gotham_send_analytics === 'function') {
            gotham_send_analytics('popup_closed', 'user_action');
            log('Sent popup_closed event', 'success');
        }
        
        // Simulate checking if adblock is disabled (in real scenario, this would be actual detection)
        setTimeout(() => {
            log('Simulating adblock check...', 'info');
            
            // Simulate successful adblock disable
            if (typeof window.gotham_adblock_conversion === 'function') {
                window.gotham_adblock_conversion();
                log('Sent adblock_disabled conversion event', 'success');
            }
            
            hideTestPopup();
            log('Test conversion completed!', 'success');
        }, 1000);
    }
    
    function testDirectTracking() {
        log('Testing direct analytics tracking...', 'info');
        
        if (typeof gotham_send_analytics === 'function') {
            // Test different event types
            gotham_send_analytics('pop_displayed', 'test');
            log('Sent test pop_displayed event', 'success');
            
            setTimeout(() => {
                gotham_send_analytics('popup_closed', 'test');
                log('Sent test popup_closed event', 'success');
            }, 1000);
            
            setTimeout(() => {
                gotham_send_analytics('adblock_disabled', 'test');
                log('Sent test adblock_disabled event', 'success');
            }, 2000);
        } else {
            log('gotham_send_analytics function not available', 'error');
        }
    }
    
    function clearConsole() {
        document.getElementById('console-output').innerHTML = 'Console cleared...\n';
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        log('Test page initialized', 'success');
        
        // Check if analytics tracker is loaded
        setTimeout(() => {
            if (typeof gotham_send_analytics === 'function') {
                log('Analytics tracker loaded successfully', 'success');
            } else {
                log('Analytics tracker not loaded - check console for errors', 'error');
            }
        }, 1000);
    });
    
    // Override console methods to capture logs
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;
    
    console.log = function(...args) {
        originalLog.apply(console, args);
        if (args[0] && args[0].includes && args[0].includes('Gotham Analytics')) {
            log(args.join(' '), 'info');
        }
    };
    
    console.warn = function(...args) {
        originalWarn.apply(console, args);
        if (args[0] && args[0].includes && args[0].includes('Gotham Analytics')) {
            log(args.join(' '), 'warning');
        }
    };
    
    console.error = function(...args) {
        originalError.apply(console, args);
        if (args[0] && args[0].includes && args[0].includes('Gotham Analytics')) {
            log(args.join(' '), 'error');
        }
    };
    </script>
</body>
</html>
