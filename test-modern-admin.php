<?php
/**
 * Test Modern Admin Interface
 * This file can be accessed via: /wp-content/plugins/gotham-block-extra-light/test-modern-admin.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-load.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Gotham Admin Interface Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .test-link { display: inline-block; margin: 10px 0; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; }
        .test-link:hover { background: #005a87; color: white; }
    </style>
</head>
<body>
    <h1>🎨 Gotham Modern Admin Interface Test</h1>
    
    <?php
    $plugin_dir = plugin_dir_path(__FILE__);
    $plugin_url = plugin_dir_url(__FILE__);
    
    // Test 1: Check if modern CSS file exists
    echo '<div class="test-section">';
    echo '<h2>📁 Modern Admin Assets Test</h2>';
    
    $assets = [
        'Modern CSS' => $plugin_dir . 'assets/gotham-admin.css',
        'Modern JavaScript' => $plugin_dir . 'assets/gotham-admin.js',
        'Analytics CSS' => $plugin_dir . 'assets/analytics-admin.css',
        'Analytics JavaScript' => $plugin_dir . 'assets/analytics-admin.js'
    ];
    
    foreach ($assets as $type => $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            echo '<div class="success">✅ ' . $type . ' file exists (' . number_format($size) . ' bytes)</div>';
        } else {
            echo '<div class="error">❌ ' . $type . ' file missing: ' . basename($file) . '</div>';
        }
    }
    echo '</div>';
    
    // Test 2: Check WordPress hooks
    echo '<div class="test-section">';
    echo '<h2>🔌 WordPress Admin Hooks Test</h2>';
    
    $hooks_to_check = [
        'admin_enqueue_scripts' => 'Script/style enqueuing for admin',
        'admin_menu' => 'Admin menu registration',
        'admin_init' => 'Admin initialization'
    ];
    
    foreach ($hooks_to_check as $hook => $description) {
        if (has_action($hook)) {
            echo '<div class="success">✅ Hook registered: ' . $hook . ' (' . $description . ')</div>';
        } else {
            echo '<div class="warning">⚠️ Hook may not be registered: ' . $hook . ' (' . $description . ')</div>';
        }
    }
    echo '</div>';
    
    // Test 3: Check admin page accessibility
    echo '<div class="test-section">';
    echo '<h2>🔗 Admin Page Access Test</h2>';
    
    $admin_url = admin_url('admin.php?page=gotham-plugin');
    $analytics_url = admin_url('admin.php?page=gotham-adblock-analytics');
    
    echo '<div class="info">Main configuration page URL: <code>' . $admin_url . '</code></div>';
    echo '<div class="info">Analytics dashboard URL: <code>' . $analytics_url . '</code></div>';
    
    echo '<a href="' . $admin_url . '" class="test-link">🎛️ Test Main Configuration Page</a><br>';
    echo '<a href="' . $analytics_url . '" class="test-link">📊 Test Analytics Dashboard</a>';
    echo '</div>';
    
    // Test 4: Check current plugin options
    echo '<div class="test-section">';
    echo '<h2>⚙️ Current Plugin Settings Test</h2>';
    
    $options = [
        'gothamadblock_option_fury' => 'Detection Mode',
        'gothamadblock_option_cookietime' => 'Cookie Time (SSJ1)',
        'gothamadblock_option_messageperso_title' => 'Custom Title',
        'gothamadblock_option_messageperso' => 'Custom Message',
        'gothamadblock_option_messageperso_button' => 'Custom Button'
    ];
    
    foreach ($options as $option_name => $description) {
        $value = get_option($option_name);
        if ($value !== false && $value !== '') {
            echo '<div class="success">✅ ' . $description . ': <code>' . esc_html(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') . '</code></div>';
        } else {
            echo '<div class="info">ℹ️ ' . $description . ': <em>Not set (will use defaults)</em></div>';
        }
    }
    echo '</div>';
    
    // Test 5: CSS/JS Loading Test
    echo '<div class="test-section">';
    echo '<h2>🎨 Style and Script Loading Test</h2>';
    
    // Simulate admin page load
    $current_screen = 'toplevel_page_gotham-plugin';
    
    echo '<div class="info">Simulating admin page load for: <code>' . $current_screen . '</code></div>';
    
    // Check if styles would be loaded
    $css_url = $plugin_url . 'assets/gotham-admin.css';
    $js_url = $plugin_url . 'assets/gotham-admin.js';
    
    echo '<div class="success">✅ Modern CSS would load from: <code>' . $css_url . '</code></div>';
    echo '<div class="success">✅ Modern JavaScript would load from: <code>' . $js_url . '</code></div>';
    
    // Test if URLs are accessible
    $css_headers = @get_headers($css_url);
    $js_headers = @get_headers($js_url);
    
    if ($css_headers && strpos($css_headers[0], '200') !== false) {
        echo '<div class="success">✅ CSS file is accessible via HTTP</div>';
    } else {
        echo '<div class="warning">⚠️ CSS file may not be accessible via HTTP (check file permissions)</div>';
    }
    
    if ($js_headers && strpos($js_headers[0], '200') !== false) {
        echo '<div class="success">✅ JavaScript file is accessible via HTTP</div>';
    } else {
        echo '<div class="warning">⚠️ JavaScript file may not be accessible via HTTP (check file permissions)</div>';
    }
    echo '</div>';
    
    // Test 6: Modern Interface Features
    echo '<div class="test-section">';
    echo '<h2>✨ Modern Interface Features</h2>';
    
    $features = [
        '🎨 Card-based layout' => 'Modern card design for better organization',
        '⚡ Interactive mode selection' => 'Visual mode cards instead of dropdown',
        '📱 Responsive design' => 'Mobile and tablet compatibility',
        '🎯 Form validation' => 'Client-side validation and error handling',
        '🔔 Smart notifications' => 'Contextual feedback messages',
        '🎭 Smooth animations' => 'CSS transitions and hover effects',
        '🌈 Modern color scheme' => 'Contemporary WordPress admin styling',
        '📝 Improved typography' => 'Better fonts and spacing'
    ];
    
    foreach ($features as $feature => $description) {
        echo '<div class="success">✅ ' . $feature . ': ' . $description . '</div>';
    }
    echo '</div>';
    
    // Test 7: Browser Compatibility
    echo '<div class="test-section">';
    echo '<h2>🌐 Browser Compatibility</h2>';
    
    echo '<div class="info">The modern interface uses:</div>';
    echo '<ul>';
    echo '<li>✅ CSS Grid and Flexbox (supported in all modern browsers)</li>';
    echo '<li>✅ CSS Custom Properties (supported in IE11+ and all modern browsers)</li>';
    echo '<li>✅ ES6 JavaScript features with fallbacks</li>';
    echo '<li>✅ Progressive enhancement for older browsers</li>';
    echo '</ul>';
    echo '</div>';
    ?>
    
    <div class="test-section">
        <h2>🚀 Next Steps</h2>
        <div class="info">
            <p><strong>If all tests pass:</strong></p>
            <ol>
                <li>✅ Go to <a href="<?php echo admin_url('admin.php?page=gotham-plugin'); ?>">Main Configuration Page</a></li>
                <li>✅ You should see the modern card-based interface</li>
                <li>✅ Test the interactive mode selection cards</li>
                <li>✅ Verify the responsive design on different screen sizes</li>
                <li>✅ Check the <a href="<?php echo admin_url('admin.php?page=gotham-adblock-analytics'); ?>">Analytics Dashboard</a> for consistency</li>
            </ol>
            
            <p><strong>Key improvements you'll see:</strong></p>
            <ul>
                <li>🎨 Beautiful card-based layout instead of old table design</li>
                <li>⚡ Interactive mode selection with visual feedback</li>
                <li>📱 Responsive design that works on all devices</li>
                <li>🎯 Better form controls with validation</li>
                <li>🔔 Smart notifications and user feedback</li>
                <li>✨ Smooth animations and modern styling</li>
            </ul>
            
            <p><strong>If you encounter issues:</strong></p>
            <ul>
                <li>🔧 Clear browser cache and WordPress cache</li>
                <li>🔧 Check browser console for JavaScript errors</li>
                <li>🔧 Verify file permissions for CSS/JS assets</li>
                <li>🔧 Ensure WordPress admin permissions are correct</li>
            </ul>
        </div>
    </div>
    
</body>
</html>
