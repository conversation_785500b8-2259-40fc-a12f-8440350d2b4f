<?php
/**
 * Gotham Block Analytics Admin Class
 * Handles analytics dashboard, DB, and AJAX for the plugin
 */
if (!defined('ABSPATH')) exit;

class Gotham_Analytics_Admin {
    private $has_bot_detection = false;

    public function __construct() {
        // Remove duplicate menu registration - handled by main plugin file
        add_action('admin_enqueue_scripts', [$this, 'enqueue_assets']);
        add_action('wp_ajax_gotham_adblock_get_stats', [$this, 'ajax_get_stats']);
        // Create table on plugin activation
        $this->create_table();
    }

    public function enqueue_assets($hook) {
        // Correct hook for the Analytics submenu page
        if ($hook !== 'g-blockadblock_page_gotham-analytics') return;

        wp_enqueue_style('gotham-analytics-admin', plugin_dir_url(__FILE__) . '../assets/analytics-admin.css');
        wp_enqueue_script('chartjs', 'https://cdn.jsdelivr.net/npm/chart.js', [], null, true);
        wp_enqueue_script('apexcharts', 'https://cdn.jsdelivr.net/npm/apexcharts', [], null, true);
        wp_enqueue_script('jsvectormap', 'https://cdn.jsdelivr.net/npm/jsvectormap', [], null, true);
        wp_enqueue_script('gotham-analytics-admin', plugin_dir_url(__FILE__) . '../assets/analytics-admin.js', ['chartjs','apexcharts','jsvectormap'], null, true);
        wp_localize_script('gotham-analytics-admin', 'gothamAnalyticsAjax', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gotham_analytics')
        ]);
    }

    public function create_table() {
        // Table creation is now handled by the main plugin file
        // This method is kept for backward compatibility but does nothing
        return;
    }

    public function dashboard_page() {
        ?>
        <div class="wrap gotham-analytics-admin">
            <h1>Adblock Analytics Dashboard</h1>

            <!-- Dashboard Controls -->
            <div class="gotham-controls">
                <div class="control-group">
                    <label for="period-selector">Time Period:</label>
                    <select id="period-selector">
                        <option value="daily">Daily (Last 30 days)</option>
                        <option value="hourly">Hourly (Last 24 hours)</option>
                        <option value="weekly">Weekly (Last 12 weeks)</option>
                    </select>
                </div>

                <div class="control-group">
                    <label for="days-selector">Days to Show:</label>
                    <select id="days-selector">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                </div>

                <div class="control-group">
                    <button id="refresh-data" class="button button-primary">Refresh Data</button>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div id="loading-indicator" style="display: none;">
                <p>Loading analytics data...</p>
            </div>

            <!-- Summary Cards -->
            <div id="summary-cards" class="summary-grid">
                <!-- Default "no data" state -->
                <div class="summary-card total-detected">
                    <div class="metric-value">0</div>
                    <div class="metric-label">Total Users Detected</div>
                    <div class="metric-description">Unique human users who visited with adblock enabled</div>
                </div>
                <div class="summary-card popup-displayed">
                    <div class="metric-value">0</div>
                    <div class="metric-label">Adblock Pop Displayed</div>
                    <div class="metric-description">Unique human users who saw the popup</div>
                </div>
                <div class="summary-card conversion">
                    <div class="metric-value">0</div>
                    <div class="metric-label">Adblock Disabled</div>
                    <div class="metric-description">Unique human users who disabled adblock (verified)</div>
                </div>
                <div class="summary-card rate">
                    <div class="metric-value">0%</div>
                    <div class="metric-label">Conversion Rate</div>
                    <div class="metric-description">% of popup viewers who disabled adblock</div>
                </div>
            </div>

            <!-- Data Quality Section -->
            <div class="data-quality-section">
                <h3>📊 Data Quality & Bot Filtering</h3>
                <div class="quality-metrics">
                    <div class="quality-card">
                        <div class="metric-value" id="bot-filter-rate">0%</div>
                        <div class="metric-label">Bot Traffic Filtered</div>
                        <div class="metric-description">Percentage of requests identified as bots</div>
                    </div>
                    <div class="quality-card">
                        <div class="metric-value" id="total-bot-requests">0</div>
                        <div class="metric-label">Bot Requests Blocked</div>
                        <div class="metric-description">Total automated requests filtered out</div>
                    </div>
                    <div class="quality-card">
                        <div class="metric-value" id="unique-bot-ips">0</div>
                        <div class="metric-label">Unique Bot IPs</div>
                        <div class="metric-description">Distinct IP addresses identified as bots</div>
                    </div>
                    <div class="quality-card human-verified">
                        <div class="metric-value">✓</div>
                        <div class="metric-label">Human-Only Analytics</div>
                        <div class="metric-description">All metrics show verified human users only</div>
                    </div>
                </div>
            </div>

            <!-- Main Dashboard -->
            <div id="gotham-analytics-dashboard" class="dashboard-grid">
                <!-- Default "no data" state -->
                <div class="gotham-analytics-card">
                    <div class="gotham-analytics-title">Analytics Dashboard</div>
                    <div style="text-align: center; padding: 50px; color: #666;">
                        <p><strong>Welcome to Gotham Block Analytics!</strong></p>
                        <p>No analytics data available yet. Data will appear here once users start interacting with your adblock detection popup.</p>
                        <p>Make sure your plugin is active and configured to start collecting data.</p>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div id="error-message" class="notice notice-error" style="display: none;">
                <p>Failed to load analytics data. Please try again.</p>
            </div>
        </div>
        <?php
    }

    public function ajax_get_stats() {
        // Verify nonce for security
        if (!wp_verify_nonce($_GET['_wpnonce'] ?? '', 'gotham_analytics')) {
            wp_send_json_error(['message' => 'Invalid nonce']);
            return;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
            // Return empty data structure instead of error
            $empty_data = [
                'summary' => [
                    'total_users_detected' => 0,
                    'popup_displayed_users' => 0,
                    'adblock_disabled_users' => 0,
                    'popup_declined_users' => 0,
                    'conversion_rate' => 0,
                    'total_bot_requests' => 0,
                    'unique_bot_ips' => 0,
                    'bot_filter_rate' => 0,
                    'user_types' => []
                ],
                'time_series' => [],
                'funnel' => [
                    'popup_displayed' => 0,
                    'interacted' => 0,
                    'converted' => 0,
                    'interaction_rate' => 0,
                    'conversion_rate' => 0
                ],
                'geographic' => [],
                'browsers' => [],
                'user_types' => [],
                'debug' => [
                    'table_exists' => false,
                    'table_name' => $table
                ]
            ];
            wp_send_json($empty_data);
            return;
        }

        // Check if table has bot detection columns
        $columns = $wpdb->get_results("DESCRIBE $table");
        $column_names = array_column($columns, 'Field');
        $has_bot_columns = in_array('is_bot', $column_names);

        // Set global flag for queries
        $this->has_bot_detection = $has_bot_columns;

        $period = sanitize_text_field($_GET['period'] ?? 'daily');
        $days = intval($_GET['days'] ?? 30);

        try {
            $summary = $this->get_summary_stats($days);

            // Add debugging information
            $debug_info = [
                'table_exists' => true,
                'table_name' => $table,
                'has_bot_detection' => $this->has_bot_detection,
                'total_records' => $wpdb->get_var("SELECT COUNT(*) FROM $table"),
                'date_filter' => date('Y-m-d H:i:s', strtotime("-$days days")),
                'columns' => $column_names,
                'last_error' => $wpdb->last_error,
                'summary_stats' => $summary
            ];

            $data = [
                'summary' => $summary,
                'time_series' => $this->get_time_series_data($period, $days),
                'funnel' => $this->get_conversion_funnel($days),
                'geographic' => $this->get_geographic_data($days),
                'browsers' => $this->get_browser_data($days),
                'user_types' => $this->get_user_type_data($days),
                'debug' => $debug_info
            ];

            wp_send_json($data);
        } catch (Exception $e) {
            wp_send_json_error([
                'message' => 'Database error: ' . $e->getMessage(),
                'debug' => [
                    'table_name' => $table,
                    'has_bot_detection' => $this->has_bot_detection ?? false,
                    'last_error' => $wpdb->last_error ?? 'No error'
                ]
            ]);
        }
    }

    private function get_summary_stats($days = 30) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        $stats = [];

        // Build WHERE clause based on bot detection capability
        $bot_filter = $this->has_bot_detection ? "is_bot = 0 AND" : "";

        // 1. "Total Users Detected" - Unique users who triggered adblock detection
        $stats['total_users_detected'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE $bot_filter created_at > %s",
            $date_filter
        ));

        // 2. "Adblock Pop Displayed" - Unique users who actually saw the popup
        $stats['popup_displayed_users'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE $bot_filter event_type = 'pop_displayed' AND created_at > %s",
            $date_filter
        ));

        // 3. "Adblock Disabled" - Unique users who successfully disabled adblock (verified)
        $stats['adblock_disabled_users'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE $bot_filter event_type = 'adblock_disabled' AND created_at > %s",
            $date_filter
        ));

        // Additional metric: Users who declined (clicked close/cancel)
        $stats['popup_declined_users'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE $bot_filter event_type = 'popup_closed' AND created_at > %s",
            $date_filter
        ));

        // Bot traffic statistics for monitoring (only if bot detection is available)
        if ($this->has_bot_detection) {
            $stats['total_bot_requests'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table WHERE is_bot = 1 AND created_at > %s",
                $date_filter
            ));

            $stats['unique_bot_ips'] = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE is_bot = 1 AND created_at > %s",
                $date_filter
            ));

            // Data quality metrics
            $total_requests = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table WHERE created_at > %s",
                $date_filter
            ));

            $stats['bot_filter_rate'] = $total_requests > 0 ?
                round(($stats['total_bot_requests'] / $total_requests) * 100, 2) : 0;
        } else {
            // No bot detection available
            $stats['total_bot_requests'] = 0;
            $stats['unique_bot_ips'] = 0;
            $stats['bot_filter_rate'] = 0;
        }

        // Conversion rates
        $stats['popup_display_rate'] = $stats['total_users_detected'] > 0 ?
            round(($stats['popup_displayed_users'] / $stats['total_users_detected']) * 100, 2) : 0;

        $stats['conversion_rate'] = $stats['popup_displayed_users'] > 0 ?
            round(($stats['adblock_disabled_users'] / $stats['popup_displayed_users']) * 100, 2) : 0;

        $stats['overall_conversion_rate'] = $stats['total_users_detected'] > 0 ?
            round(($stats['adblock_disabled_users'] / $stats['total_users_detected']) * 100, 2) : 0;

        // User type breakdown
        $user_types = $wpdb->get_results($wpdb->prepare(
            "SELECT user_type, COUNT(DISTINCT ip_address) as count FROM $table
             WHERE created_at > %s GROUP BY user_type",
            $date_filter
        ), ARRAY_A);

        $stats['user_types'] = [];
        foreach ($user_types as $type) {
            $stats['user_types'][$type['user_type']] = intval($type['count']);
        }

        return $stats;
    }

    private function get_time_series_data($period, $days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';

        switch ($period) {
            case 'hourly':
                return $this->get_hourly_data($days);
            case 'weekly':
                return $this->get_weekly_data($days);
            default:
                return $this->get_daily_data($days);
        }
    }

    private function get_daily_data($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d', strtotime("-$days days"));

        $sql = "SELECT
                    DATE(created_at) as date,
                    event_type,
                    COUNT(DISTINCT ip_address) as count
                FROM $table
                WHERE DATE(created_at) >= %s
                GROUP BY DATE(created_at), event_type
                ORDER BY date ASC";

        $results = $wpdb->get_results($wpdb->prepare($sql, $date_filter), ARRAY_A);

        return $this->format_time_series_data($results, 'date');
    }

    private function get_hourly_data($hours = 24) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$hours hours"));

        $sql = "SELECT
                    DATE_FORMAT(created_at, '%%Y-%%m-%%d %%H:00:00') as hour,
                    event_type,
                    COUNT(DISTINCT ip_address) as count
                FROM $table
                WHERE created_at >= %s
                GROUP BY DATE_FORMAT(created_at, '%%Y-%%m-%%d %%H:00:00'), event_type
                ORDER BY hour ASC";

        $results = $wpdb->get_results($wpdb->prepare($sql, $date_filter), ARRAY_A);

        return $this->format_time_series_data($results, 'hour');
    }

    private function get_weekly_data($weeks = 12) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d', strtotime("-$weeks weeks"));

        $sql = "SELECT
                    DATE_FORMAT(created_at, '%%Y-%%u') as week,
                    event_type,
                    COUNT(DISTINCT ip_address) as count
                FROM $table
                WHERE DATE(created_at) >= %s
                GROUP BY DATE_FORMAT(created_at, '%%Y-%%u'), event_type
                ORDER BY week ASC";

        $results = $wpdb->get_results($wpdb->prepare($sql, $date_filter), ARRAY_A);

        return $this->format_time_series_data($results, 'week');
    }

    private function format_time_series_data($results, $time_key) {
        $formatted = [];
        $events = ['pop_displayed', 'adblock_disabled', 'popup_closed'];

        foreach ($results as $row) {
            $time = $row[$time_key];
            if (!isset($formatted[$time])) {
                $formatted[$time] = [
                    'time' => $time,
                    'pop_displayed' => 0,
                    'adblock_disabled' => 0,
                    'popup_closed' => 0
                ];
            }
            $formatted[$time][$row['event_type']] = intval($row['count']);
        }

        return array_values($formatted);
    }

    private function get_conversion_funnel($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        // Get funnel data
        $funnel = [];

        // Step 1: Users who saw popup
        $funnel['popup_displayed'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = 'pop_displayed' AND created_at > %s",
            $date_filter
        ));

        // Step 2: Users who interacted (either converted or declined)
        $funnel['interacted'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table
             WHERE event_type IN ('adblock_disabled', 'popup_closed') AND created_at > %s",
            $date_filter
        ));

        // Step 3: Users who converted
        $funnel['converted'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT ip_address) FROM $table WHERE event_type = 'adblock_disabled' AND created_at > %s",
            $date_filter
        ));

        // Calculate percentages
        $funnel['interaction_rate'] = $funnel['popup_displayed'] > 0 ?
            round(($funnel['interacted'] / $funnel['popup_displayed']) * 100, 2) : 0;
        $funnel['conversion_rate'] = $funnel['popup_displayed'] > 0 ?
            round(($funnel['converted'] / $funnel['popup_displayed']) * 100, 2) : 0;

        return $funnel;
    }

    private function get_geographic_data($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        $countries = $wpdb->get_results($wpdb->prepare(
            "SELECT country,
                    COUNT(DISTINCT ip_address) as total_users,
                    SUM(CASE WHEN event_type = 'adblock_disabled' THEN 1 ELSE 0 END) as conversions
             FROM $table
             WHERE country != '' AND country != 'Unknown' AND created_at > %s
             GROUP BY country
             ORDER BY total_users DESC
             LIMIT 20",
            $date_filter
        ), ARRAY_A);

        $geographic_data = [];
        foreach ($countries as $country) {
            $geographic_data[$country['country']] = [
                'users' => intval($country['total_users']),
                'conversions' => intval($country['conversions']),
                'conversion_rate' => $country['total_users'] > 0 ?
                    round(($country['conversions'] / $country['total_users']) * 100, 2) : 0
            ];
        }

        return $geographic_data;
    }

    private function get_browser_data($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        // Build WHERE clause based on bot detection capability
        $bot_filter = $this->has_bot_detection ? "is_bot = 0 AND" : "";

        // Get raw browser data for re-processing
        $raw_browsers = $wpdb->get_results($wpdb->prepare(
            "SELECT browser, browser_version, user_agent,
                    COUNT(DISTINCT ip_address) as total_users,
                    SUM(CASE WHEN event_type = 'adblock_disabled' THEN 1 ELSE 0 END) as conversions,
                    SUM(CASE WHEN is_mobile = 1 THEN 1 ELSE 0 END) as mobile_users
             FROM $table
             WHERE $bot_filter browser != '' AND browser != 'Unknown' AND created_at > %s
             GROUP BY browser, browser_version, user_agent
             ORDER BY total_users DESC",
            $date_filter
        ), ARRAY_A);

        // Group browsers by type (without versions)
        $browser_groups = [];
        foreach ($raw_browsers as $browser_record) {
            // Clean up browser name to remove version
            $browser_type = $this->clean_browser_name($browser_record['browser']);

            // Aggregate data by browser type
            if (!isset($browser_groups[$browser_type])) {
                $browser_groups[$browser_type] = [
                    'users' => 0,
                    'conversions' => 0,
                    'mobile_users' => 0
                ];
            }

            $browser_groups[$browser_type]['users'] += intval($browser_record['total_users']);
            $browser_groups[$browser_type]['conversions'] += intval($browser_record['conversions']);
            $browser_groups[$browser_type]['mobile_users'] += intval($browser_record['mobile_users']);
        }

        // Calculate conversion rates and prepare final data
        $browser_data = [];
        foreach ($browser_groups as $browser_type => $data) {
            $browser_data[$browser_type] = [
                'users' => $data['users'],
                'conversions' => $data['conversions'],
                'mobile_users' => $data['mobile_users'],
                'conversion_rate' => $data['users'] > 0 ?
                    round(($data['conversions'] / $data['users']) * 100, 2) : 0
            ];
        }

        // Sort by user count (descending)
        uasort($browser_data, function($a, $b) {
            return $b['users'] - $a['users'];
        });

        // Limit to top 15 browsers
        return array_slice($browser_data, 0, 15, true);
    }

    // Helper function to clean browser names and group by type
    private function clean_browser_name($browser_name) {
        if (empty($browser_name)) return 'Unknown';

        // Remove version numbers and common suffixes
        $browser_name = preg_replace('/\s+\d+[\d\.\s]*$/', '', $browser_name);
        $browser_name = preg_replace('/\s+(Legacy|Mobile|Browser)$/', '', $browser_name);

        // Standardize common browser names (case-insensitive matching)
        $browser_mappings = [
            'Microsoft Edge Legacy' => 'Microsoft Edge',
            'Chrome Mobile' => 'Chrome',
            'Firefox Mobile' => 'Firefox',
            'Safari Mobile' => 'Safari',
            'Opera Mobile' => 'Opera',
            'Internet Explorer Mobile' => 'Internet Explorer'
        ];

        foreach ($browser_mappings as $old_name => $new_name) {
            if (stripos($browser_name, $old_name) !== false) {
                return $new_name;
            }
        }

        // Handle partial matches for major browsers
        if (stripos($browser_name, 'chrome') !== false) return 'Chrome';
        if (stripos($browser_name, 'firefox') !== false) return 'Firefox';
        if (stripos($browser_name, 'safari') !== false) return 'Safari';
        if (stripos($browser_name, 'edge') !== false) return 'Microsoft Edge';
        if (stripos($browser_name, 'opera') !== false) return 'Opera';
        if (stripos($browser_name, 'internet explorer') !== false || stripos($browser_name, 'msie') !== false) return 'Internet Explorer';
        if (stripos($browser_name, 'samsung') !== false) return 'Samsung Internet';
        if (stripos($browser_name, 'brave') !== false) return 'Brave';
        if (stripos($browser_name, 'vivaldi') !== false) return 'Vivaldi';

        return $browser_name;
    }

    private function get_user_type_data($days) {
        global $wpdb;
        $table = $wpdb->prefix . 'gotham_adblock_stats';
        $date_filter = date('Y-m-d H:i:s', strtotime("-$days days"));

        $user_types = $wpdb->get_results($wpdb->prepare(
            "SELECT user_type,
                    COUNT(DISTINCT ip_address) as total_users,
                    SUM(CASE WHEN event_type = 'adblock_disabled' THEN 1 ELSE 0 END) as conversions
             FROM $table
             WHERE created_at > %s
             GROUP BY user_type
             ORDER BY total_users DESC",
            $date_filter
        ), ARRAY_A);

        $type_data = [];
        foreach ($user_types as $type) {
            $type_data[$type['user_type']] = [
                'users' => intval($type['total_users']),
                'conversions' => intval($type['conversions']),
                'conversion_rate' => $type['total_users'] > 0 ?
                    round(($type['conversions'] / $type['total_users']) * 100, 2) : 0
            ];
        }

        return $type_data;
    }
}

// Initialize in admin
if (is_admin()) {
    new Gotham_Analytics_Admin();
}
