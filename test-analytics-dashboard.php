<?php
/**
 * Test Analytics Dashboard Functionality
 * This file can be accessed via: /wp-content/plugins/gotham-block-extra-light/test-analytics-dashboard.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-load.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Gotham Analytics Dashboard Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Gotham Analytics Dashboard Test</h1>
    
    <?php
    global $wpdb;
    $table_name = $wpdb->prefix . 'gotham_adblock_stats';
    
    // Test 1: Check if table exists
    echo '<div class="test-section">';
    echo '<h2>📊 Database Table Test</h2>';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if ($table_exists) {
        echo '<div class="success">✅ Database table exists: ' . $table_name . '</div>';
        
        // Show table structure
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
        echo '<h3>Table Structure:</h3>';
        echo '<pre>';
        foreach ($columns as $column) {
            echo $column->Field . ' (' . $column->Type . ')' . "\n";
        }
        echo '</pre>';
        
        // Show record count
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        echo '<div class="info">📈 Total records in table: ' . $count . '</div>';
        
    } else {
        echo '<div class="error">❌ Database table does not exist. Please activate the plugin to create the table.</div>';
    }
    echo '</div>';
    
    // Test 2: Check AJAX endpoint
    echo '<div class="test-section">';
    echo '<h2>🔗 AJAX Endpoint Test</h2>';
    
    // Simulate AJAX call
    $_GET['action'] = 'gotham_adblock_get_stats';
    $_GET['period'] = 'daily';
    $_GET['days'] = 30;
    $_GET['_wpnonce'] = wp_create_nonce('gotham_analytics');
    
    if (class_exists('Gotham_Analytics_Admin')) {
        $analytics = new Gotham_Analytics_Admin();
        
        ob_start();
        $analytics->ajax_get_stats();
        $output = ob_get_clean();
        
        if (!empty($output)) {
            $data = json_decode($output, true);
            if ($data) {
                echo '<div class="success">✅ AJAX endpoint working correctly</div>';
                echo '<h3>Sample Data Response:</h3>';
                echo '<pre>' . json_encode($data, JSON_PRETTY_PRINT) . '</pre>';
            } else {
                echo '<div class="error">❌ AJAX endpoint returned invalid JSON</div>';
                echo '<pre>' . htmlspecialchars($output) . '</pre>';
            }
        } else {
            echo '<div class="error">❌ AJAX endpoint returned no data</div>';
        }
    } else {
        echo '<div class="error">❌ Gotham_Analytics_Admin class not found</div>';
    }
    echo '</div>';
    
    // Test 3: Check file assets
    echo '<div class="test-section">';
    echo '<h2>📁 Asset Files Test</h2>';
    
    $plugin_dir = plugin_dir_path(__FILE__);
    $assets = [
        'CSS' => $plugin_dir . 'assets/analytics-admin.css',
        'JavaScript' => $plugin_dir . 'assets/analytics-admin.js',
        'Tracker' => $plugin_dir . 'analytics-tracker.js'
    ];
    
    foreach ($assets as $type => $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            echo '<div class="success">✅ ' . $type . ' file exists (' . number_format($size) . ' bytes)</div>';
        } else {
            echo '<div class="error">❌ ' . $type . ' file missing: ' . basename($file) . '</div>';
        }
    }
    echo '</div>';
    
    // Test 4: Check WordPress hooks
    echo '<div class="test-section">';
    echo '<h2>🔌 WordPress Hooks Test</h2>';
    
    $hooks_to_check = [
        'wp_ajax_gotham_adblock_get_stats' => 'AJAX action for analytics data',
        'wp_ajax_gotham_adblock_track_event' => 'AJAX action for event tracking',
        'admin_menu' => 'Admin menu registration',
        'admin_enqueue_scripts' => 'Script/style enqueuing'
    ];
    
    foreach ($hooks_to_check as $hook => $description) {
        if (has_action($hook)) {
            echo '<div class="success">✅ Hook registered: ' . $hook . ' (' . $description . ')</div>';
        } else {
            echo '<div class="error">❌ Hook missing: ' . $hook . ' (' . $description . ')</div>';
        }
    }
    echo '</div>';
    
    // Test 5: Insert sample data for testing
    if ($table_exists && $count == 0) {
        echo '<div class="test-section">';
        echo '<h2>🧪 Sample Data Test</h2>';
        echo '<div class="info">No data found. Would you like to insert sample data for testing?</div>';
        
        if (isset($_GET['insert_sample'])) {
            // Insert sample data
            $sample_data = [
                ['pop_displayed', '***********', 'Chrome', '91.0', 'Windows', 'US'],
                ['adblock_disabled', '***********', 'Chrome', '91.0', 'Windows', 'US'],
                ['pop_displayed', '***********', 'Firefox', '89.0', 'Mac', 'CA'],
                ['popup_closed', '***********', 'Firefox', '89.0', 'Mac', 'CA'],
                ['pop_displayed', '***********', 'Safari', '14.1', 'iOS', 'UK'],
            ];
            
            $inserted = 0;
            foreach ($sample_data as $data) {
                $result = $wpdb->insert(
                    $table_name,
                    [
                        'event_type' => $data[0],
                        'ip_address' => $data[1],
                        'user_agent' => $data[2] . '/' . $data[3],
                        'browser' => $data[2],
                        'browser_version' => $data[3],
                        'os' => $data[4],
                        'country' => $data[5],
                        'status' => 'completed',
                        'user_type' => 'unique',
                        'session_id' => 'test_' . uniqid(),
                        'referrer_url' => 'https://example.com',
                        'page_url' => 'https://yoursite.com/test',
                        'session_duration' => rand(30, 300),
                        'is_mobile' => ($data[4] == 'iOS' ? 1 : 0),
                        'screen_resolution' => '1920x1080',
                        'timezone' => 'America/New_York',
                        'created_at' => current_time('mysql'),
                        'updated_at' => current_time('mysql')
                    ]
                );
                if ($result) $inserted++;
            }
            
            echo '<div class="success">✅ Inserted ' . $inserted . ' sample records</div>';
            echo '<div class="info">🔄 <a href="' . admin_url('admin.php?page=gotham-adblock-analytics') . '">View Analytics Dashboard</a></div>';
        } else {
            echo '<div class="info">🔗 <a href="?insert_sample=1">Click here to insert sample data</a></div>';
        }
        echo '</div>';
    }
    ?>
    
    <div class="test-section">
        <h2>🚀 Next Steps</h2>
        <div class="info">
            <p><strong>If all tests pass:</strong></p>
            <ul>
                <li>✅ Go to <a href="<?php echo admin_url('admin.php?page=gotham-adblock-analytics'); ?>">Analytics Dashboard</a></li>
                <li>✅ The dashboard should load with modern styling</li>
                <li>✅ If no data exists, you'll see a friendly "no data" message</li>
                <li>✅ Once users interact with your adblock popup, data will appear</li>
            </ul>
            
            <p><strong>If you see issues:</strong></p>
            <ul>
                <li>🔧 Check that the plugin is properly activated</li>
                <li>🔧 Verify WordPress admin permissions</li>
                <li>🔧 Check browser console for JavaScript errors</li>
                <li>🔧 Ensure your site has adblock detection enabled</li>
            </ul>
        </div>
    </div>
    
</body>
</html>
