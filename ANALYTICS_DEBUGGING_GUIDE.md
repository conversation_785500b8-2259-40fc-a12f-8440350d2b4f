# 🔧 Gotham Block Analytics Dashboard - Debugging Guide

## 🚨 **Issue: Dashboard Showing Zero Values**

This guide will help you systematically debug why your analytics dashboard is showing zero values despite implementing the tracking system.

## 🔍 **Step-by-Step Debugging Process**

### **Step 1: Check Database Table Structure**

Open your browser console and run this command in the WordPress admin:

```javascript
// Test 1: Check analytics table structure and data
fetch('/wp-admin/admin-ajax.php', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: 'action=gotham_debug_analytics'
})
.then(res => res.json())
.then(data => {
    console.log('=== ANALYTICS DEBUG RESULTS ===');
    console.log('Table exists:', data.data.debug_info.table_exists);
    console.log('Total records:', data.data.debug_info.total_records);
    console.log('Human records:', data.data.debug_info.human_records);
    console.log('Bot records:', data.data.debug_info.bot_records);
    console.log('Event counts:', data.data.debug_info.event_counts);
    console.log('Table columns:', data.data.debug_info.table_columns);
    console.log('Recent records:', data.data.debug_info.recent_records);
});
```

**Expected Results:**
- ✅ `table_exists: true`
- ✅ `total_records: > 0` (if you've had popup displays)
- ✅ `table_columns` should include: `['id', 'event_type', 'ip_address', 'is_bot', 'bot_type', 'created_at', ...]`

### **Step 2: Test Analytics Tracking**

```javascript
// Test 2: Manually trigger analytics tracking
if (typeof gotham_send_analytics === 'function') {
    console.log('Analytics function available, testing...');
    gotham_send_analytics('pop_displayed', 'test');
    console.log('Test event sent');
} else {
    console.error('gotham_send_analytics function not available');
}
```

### **Step 3: Check Dashboard Data Retrieval**

```javascript
// Test 3: Check dashboard AJAX endpoint
fetch('/wp-admin/admin-ajax.php?action=gotham_adblock_get_stats&_wpnonce=' + gotham_analytics.nonce)
.then(res => res.json())
.then(data => {
    console.log('=== DASHBOARD DATA ===');
    console.log('Summary stats:', data.summary);
    console.log('Debug info:', data.debug);
    console.log('Has bot detection:', data.debug.has_bot_detection);
    console.log('Total records:', data.debug.total_records);
});
```

## 🛠️ **Common Issues and Solutions**

### **Issue 1: Table Missing Bot Detection Columns**

**Symptoms:**
- `has_bot_detection: false` in debug output
- SQL errors about unknown column 'is_bot'

**Solution:**
```javascript
// Run table migration
fetch('/wp-admin/admin-ajax.php', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: 'action=gotham_migrate_analytics_table'
})
.then(res => res.json())
.then(data => console.log('Migration result:', data));
```

### **Issue 2: No Analytics Data Being Recorded**

**Symptoms:**
- `total_records: 0` in debug output
- No events in `recent_records`

**Debugging Steps:**

1. **Check if popup is actually displaying:**
   - Enable adblock in your browser
   - Visit your website
   - Verify popup appears with ID `gothamadblock_msg`

2. **Check analytics tracker loading:**
   ```javascript
   console.log('Analytics tracker loaded:', typeof gotham_send_analytics);
   console.log('AJAX config:', window.gotham_ajax);
   ```

3. **Check browser console for errors:**
   - Look for JavaScript errors
   - Check network tab for failed AJAX requests

### **Issue 3: Analytics Tracker Not Sending Data**

**Symptoms:**
- Popup displays but no tracking events
- Console shows "Analytics function not available"

**Solution:**
Check if analytics tracker script is properly enqueued:

```php
// In gothamblock.php, verify this exists:
wp_enqueue_script('gotham-analytics-tracker', 
    plugin_dir_url(__FILE__) . 'analytics-tracker.js', 
    array('jquery'), '1.5.0', true);
```

### **Issue 4: AJAX Endpoint Not Responding**

**Symptoms:**
- Dashboard shows loading forever
- Console errors about failed AJAX requests

**Check:**
1. **AJAX URL is correct:**
   ```javascript
   console.log('AJAX URL:', gotham_analytics.ajaxurl);
   ```

2. **Nonce is valid:**
   ```javascript
   console.log('Nonce:', gotham_analytics.nonce);
   ```

3. **AJAX actions are registered:**
   - Check `wp_ajax_gotham_adblock_get_stats` is registered
   - Check `wp_ajax_gotham_adblock_track_event` is registered

## 🔧 **Manual Fixes**

### **Fix 1: Recreate Analytics Table**

If table structure is corrupted:

```javascript
// Recreate table with correct structure
fetch('/wp-admin/admin-ajax.php', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: 'action=gotham_recreate_analytics_table'
})
.then(res => res.json())
.then(data => console.log('Table recreation:', data));
```

### **Fix 2: Test Manual Event Insertion**

If tracking isn't working, test manual insertion:

```sql
-- Run this in your database (replace wp_ with your prefix)
INSERT INTO wp_gotham_adblock_stats 
(event_type, ip_address, user_agent, browser, os, country, status, user_type, is_bot, created_at) 
VALUES 
('pop_displayed', '127.0.0.1', 'Test Browser', 'Chrome', 'Windows', 'US', 'pending', 'visitor', 0, NOW()),
('adblock_disabled', '127.0.0.1', 'Test Browser', 'Chrome', 'Windows', 'US', 'completed', 'converted', 0, NOW());
```

### **Fix 3: Clear Cache and Reset**

```javascript
// Clear any cached data and reload dashboard
localStorage.clear();
sessionStorage.clear();
location.reload();
```

## 📊 **Verification Checklist**

After applying fixes, verify:

- [ ] ✅ **Table exists** with correct structure
- [ ] ✅ **Bot detection columns** present (`is_bot`, `bot_type`, `bot_confidence`)
- [ ] ✅ **Analytics tracker** loads without errors
- [ ] ✅ **Popup displays** when adblock is enabled
- [ ] ✅ **Events are tracked** (check `total_records > 0`)
- [ ] ✅ **Dashboard loads** data without errors
- [ ] ✅ **Metrics display** non-zero values

## 🎯 **Expected Behavior After Fix**

1. **When user with adblock visits:**
   - Popup displays
   - `pop_displayed` event tracked
   - "Total Users Detected" increments

2. **When user clicks popup button:**
   - `popup_closed` event tracked
   - If adblock actually disabled: `adblock_disabled` event tracked
   - "Adblock Disabled" increments

3. **Dashboard shows:**
   - Non-zero metrics
   - Recent activity in time series
   - Geographic and browser data

## 🚨 **Emergency Reset**

If all else fails, complete reset:

```javascript
// 1. Recreate table
fetch('/wp-admin/admin-ajax.php', {
    method: 'POST', 
    body: 'action=gotham_recreate_analytics_table'
});

// 2. Test tracking
setTimeout(() => {
    gotham_send_analytics('pop_displayed', 'test');
}, 2000);

// 3. Check results
setTimeout(() => {
    fetch('/wp-admin/admin-ajax.php?action=gotham_debug_analytics')
    .then(res => res.json())
    .then(data => console.log('Reset results:', data));
}, 5000);
```

---

## ✅ **Success Indicators**

Your analytics are working correctly when:
1. 🎯 Debug shows `total_records > 0`
2. 🎯 Dashboard displays non-zero metrics
3. 🎯 Events are tracked in real-time
4. 🎯 No JavaScript console errors
5. 🎯 Bot detection is functioning (`has_bot_detection: true`)

**Run through this debugging guide step by step to identify and fix the issue!**
